export interface Price {
  final_price: {
    value: number
  }
  regular_price: {
    value: number
  }
}

export interface ProductDetails {
  id: string
  name: string
  sku: string
  image: {
    url: string
  }
  custom_attributesV3: {
    items: ProductAttribute[]
  }
  __typename: 'SimpleProduct' | 'ConfigurableProduct' | 'BundleProduct' | 'VirtualProduct'
  items: {
    option_id: number
    required: boolean
    sku: string
    title: string
  }[]
}

export interface ProductBasic {
  id: string
  sku: string
  image: {
    url: string
  }
  name: string
}

export interface ProductAttribute {
  code: string
  value: string
  uid: string
}

export interface BaseOptionItem {
  value_index: number
  label: string
}

export interface ProductOptionItem extends BaseOptionItem {
  uid: string
}

export interface ProductDropDownItem {
  uid: string
  sku: string
  title: string
  price: number
  label: string
  value: string | number
}

export interface ProductConfigurableOption {
  attribute_id: string
  attribute_code: string
  label: string
  values: ProductOptionItem[]
}

export interface SafeguardItem {
  label: string
  value: string
}

export interface ProductLimitData {
  limit?: boolean
  start_time?: number
  end_time?: number
  limit_qty?: number
}

export interface ProductServiceParams {
  value: string
  sku: string
  option_id: string
  price: number
}

export interface CartProduct {
  uid: string
  quantity: number
  sku?: string
  name?: string
  price?: number
  // ... 其他产品相关字段
}

export interface ProductStatus {
  id: string
  image?: {
    url: string
  }
  price?: Price
  paymentNcoin?: boolean
  servicePrice?: number
  salable_qty?: number
  optionSelections?: Map<string, string | number>
  serviceItem?: boolean
  selectOptionName?: string
  popVisible: boolean
  sku: string
  parentSku: string
  quantity: number
  product?: CartProduct
  outOfStockVariants?: Array<Array<string | number>>
  isEverythingOutOfStock?: boolean
  isOutOfStock?: boolean
  productItem?: {
    paymeng_method?: string
  }
}

export interface Variant {
  attributes: Array<{
    code: string
    uid: string
  }>
  product: {
    image: {
      url: string
    }
    name: string
  }
  uid: string
}

export interface ProductConfigOption {
  option_id: string
  title: string
  dropDown: Array<{
    uid: string
    sku: string
    title: string
    price: number
    label: string
    value: string | number
  }>
}

export interface OptionItem {
  value_index: string | number
  label: string
  swatch_data?: {
    value: string
  }
}
