'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  IconStoreTag,
  mergeStyles,
  NCoinRange,
  NCoinView,
  PriceRange,
  PriceRanges,
  TCartProductItem,
  useLazyGetProductLimitQuery,
} from '@ninebot/core'
import { useCartProduct } from '@ninebot/core/src/businessHooks'
import type { ExtensionInfo } from '@ninebot/core/src/businessHooks/useCartProduct'

import { QuantitySelector } from '@/businessComponents'
import { Arrow } from '@/components'
import { CustomImage } from '@/components/common'
import type { RawProduct } from '@/types/cart'
import type { ProductLimitData } from '@/types/product'
// 自定义选项接口
interface CustomOption {
  label: string
  value: string | number | unknown
}

interface ProductViewProps {
  // 基础商品信息
  productData: RawProduct
  showStatus?: boolean
  quantity?: number
  // 购物车特有功能
  isEdit?: boolean
  id: string
  // 事件回调
  onQuantityChange?: (value: number) => void
  onDelete?: (id?: string) => void
  onGoProduct?: () => void
  // 是否禁用增加按钮
  isIncrementDisabled?: boolean
  handlePopup?: (
    id: string,
    sku: string,
    productData: RawProduct,
    parentSku: string,
    qty: number,
    extensionInfo: ExtensionInfo,
  ) => void
}

export default function ProductView({
  productData,
  showStatus = true,
  quantity = 1,
  // 购物车特有功能
  isEdit = true,
  id,
  // 事件回调
  onQuantityChange,
  onDelete,
  onGoProduct,
  // 禁用按钮
  isIncrementDisabled = false,
  handlePopup,
}: ProductViewProps) {
  const { product, extension_info, parent_sku } = productData

  const getI18nString = useTranslations('Common')

  // 限购相关状态
  const [limitCount, setLimitCount] = useState<number | null>(null)
  const [getProductLimit] = useLazyGetProductLimitQuery()

  // 使用 useCartProduct hook 获取商品相关信息
  const {
    isPickupProduct,
    isConfigurableProduct,
    configurableProductOptionValue,
    pickupStoreName,
    isStatus,
    isStock,
    hasCustomOptions,
    customOptionsFormatted,
    customAttributes,
    isDisplayNCoin,
    isPureNCoin,
  } = useCartProduct(
    product as unknown as TCartProductItem['product'],
    extension_info as unknown as ExtensionInfo,
  )

  // 获取商品信息
  const { name, image, price_range, sku, salable_qty } = product
  /**
   * 产品是否可用
   */
  const isAvailable = useMemo(() => {
    return isStatus && isStock
  }, [isStatus, isStock])

  /**
   * 最大购买数量（考虑库存和限购）
   */
  const inputMax = useMemo(() => {
    const salableQty = Math.min(salable_qty || 99, 99)
    return limitCount ? Math.min(limitCount, salableQty) : salableQty
  }, [limitCount, salable_qty])

  /**
   * 获取限购数量
   */
  const getLimitCount = useCallback((data: ProductLimitData) => {
    const now = new Date().getTime()
    if (
      data?.limit &&
      now / 1000 >= (data?.start_time || 0) &&
      now / 1000 <= (data?.end_time || 0)
    ) {
      setLimitCount(Number(data?.limit_qty))
    } else {
      setLimitCount(null)
    }
  }, [])

  /**
   * 获取产品限购信息
   */
  useEffect(() => {
    // 只有当产品可用且有产品ID时才查询限购信息
    if (isAvailable && product?.id) {
      getProductLimit({
        input: { product_id: Number(product?.id) },
      })
        .unwrap()
        .then((res) => {
          getLimitCount(res?.product_purchase_limit as ProductLimitData)
        })
        .catch((error) => {
          console.log('获取限购信息失败:', error)
        })
    }
  }, [product, isAvailable, getProductLimit, getLimitCount])

  /**
   * 修改数量操作
   */
  const handleQtyChange = useCallback(
    (value: number) => {
      onQuantityChange?.(value)
    },
    [onQuantityChange],
  )

  /**
   * 删除操作
   */
  const handleDelete = useCallback(() => {
    onDelete?.(id)
  }, [onDelete, id])

  /**
   * 处理商品点击
   */
  const handleProductClick = useCallback(() => {
    if (onGoProduct) {
      onGoProduct()
    }
  }, [onGoProduct])

  // 商品标题组件
  const Title = () => (
    <div className="flex items-center">
      {isPickupProduct && (
        <span className="mr-[4px]">
          <IconStoreTag className="!mr-0" />
        </span>
      )}
      <span
        className="line-clamp-1 font-miSansDemiBold450 text-[15px]"
        onClick={handleProductClick}>
        {name}
      </span>
    </div>
  )

  // 使用 useMemo 优化商品图片渲染
  const productImage = useMemo(() => {
    return (
      <div
        className="relative h-[8.8rem] w-[8.8rem] shrink-0 overflow-hidden rounded-base bg-[#f3f3f4]"
        onClick={handleProductClick}>
        <CustomImage
          src={image?.url || ''}
          alt={image?.label || ''}
          sizes="8.8rem"
          className="object-contain"
        />
        {showStatus && (
          <>
            {!isStatus && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 py-1 text-center text-xs text-white">
                {getI18nString('product_off')}
              </div>
            )}
            {isStatus && !isStock && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 py-1 text-center text-xs text-white">
                {getI18nString('out_of_stock')}
              </div>
            )}
          </>
        )}
      </div>
    )
  }, [image?.url, image?.label, showStatus, isStatus, isStock, getI18nString, handleProductClick])

  return (
    <div className="flex gap-base-12">
      {/* 商品图片 */}
      {productImage}

      {/* 商品信息 */}
      <div className="flex flex-1 flex-col justify-between gap-base">
        <Title />

        {/* 规格选择 */}
        {isAvailable ? (
          <>
            {(configurableProductOptionValue.length || pickupStoreName || hasCustomOptions) && (
              <div className="flex items-center pr-[14px]">
                <button
                  className="flex items-center gap-2 rounded-[4px] bg-gray-base px-base py-[4px] text-sm leading-[16px]"
                  onClick={() => {
                    if (!isEdit) return
                    handlePopup?.(
                      id,
                      sku,
                      productData,
                      parent_sku,
                      quantity,
                      extension_info as unknown as ExtensionInfo,
                    )
                  }}>
                  <div className="line-clamp-1 text-left text-[#6E6E73]">
                    {isConfigurableProduct && (
                      <span>{configurableProductOptionValue.join('，')}</span>
                    )}
                    {isConfigurableProduct && isPickupProduct && pickupStoreName && (
                      <span> | </span>
                    )}
                    {isPickupProduct && pickupStoreName && <span>{pickupStoreName}</span>}
                    {hasCustomOptions && (
                      <>
                        {customOptionsFormatted.map((customOption: CustomOption) => (
                          <span key={customOption.label}>
                            {(configurableProductOptionValue.length || pickupStoreName) && (
                              <span> | </span>
                            )}
                            <span>{customOption.label}：</span>
                            <span>
                              {typeof customOption.value === 'object' &&
                              customOption.value !== null &&
                              'toString' in customOption.value
                                ? customOption.value.toString()
                                : String(customOption.value)}
                            </span>
                          </span>
                        ))}
                      </>
                    )}
                  </div>
                  {isEdit && <Arrow size={12} />}
                </button>
              </div>
            )}
          </>
        ) : null}

        {/* 商品标签 */}
        {/* {extension_info?.product_tags && extension_info.product_tags.length > 0 && (
          <div className="mb-2 flex flex-wrap gap-1">
            {extension_info.product_tags.map((tag: string) => (
              <CustomTag key={tag} text={tag} />
            ))}
          </div>
        )} */}

        {/* 价格、数量和数量调节器 */}

        <div className="flex items-center">
          {isAvailable ? (
            <>
              {isPureNCoin ? (
                <NCoinRange priceRange={price_range as PriceRanges} textStyle="text-lg" />
              ) : (
                <div
                  className={mergeStyles(
                    'flex',
                    price_range?.maximum_price?.discount?.amount_off > 0
                      ? 'flex-col items-start'
                      : 'items-center gap-2',
                  )}>
                  <PriceRange
                    priceRange={price_range as PriceRanges}
                    direction="row"
                    textStyle="text-lg font-miSansDemiBold450"
                  />
                  {isDisplayNCoin && customAttributes?.max_usage_limit_ncoins && (
                    // <div className="rounded-full bg-[#f3f3f4] px-[8px] py-[3.5px]">
                    <NCoinView
                      number={Number(customAttributes.max_usage_limit_ncoins)}
                      prefixText={getI18nString('product_more_use')}
                      iconStyle={{ size: 12 }}
                      textStyle="text-sm"
                    />
                    // </div>
                  )}
                </div>
              )}
              {/* 显示数量但不显示调节器时 */}
              {!showStatus && quantity > 0 && (
                <span className="ml-auto font-miSansRegular330 text-base">×{quantity}</span>
              )}
            </>
          ) : null}
          {!isAvailable && showStatus && (
            <>
              {/* simple 产品展示状态 */}
              {isStatus && !isStock && parent_sku === sku && (
                <div className="flex w-full items-center justify-between rounded-md bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">
                    {getI18nString('product_out_of_stock')}
                  </span>
                  {isEdit && (
                    <button
                      className="rounded-md border border-red-600 bg-white px-3 py-1 text-sm text-red-600 hover:bg-red-50"
                      onClick={handleDelete}>
                      {getI18nString('delete')}
                    </button>
                  )}
                </div>
              )}
              {/* configurable 产品展示状态 */}
              {isStatus && !isStock && parent_sku !== sku && (
                <div className="flex w-full items-center justify-between rounded-md bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">{getI18nString('cart_reselect')}</span>
                  <button
                    className="rounded-md border border-primary bg-white px-3 py-1 text-sm text-primary hover:bg-primary/5"
                    onClick={() => {
                      handlePopup?.(
                        id,
                        sku,
                        productData,
                        parent_sku,
                        quantity,
                        extension_info as unknown as ExtensionInfo,
                      )
                    }}>
                    {getI18nString('reselect')}
                  </button>
                </div>
              )}
              {/* 商品下架状态 */}
              {!isStatus && (
                <div className="flex w-full items-center justify-between rounded-md bg-gray-50 p-2">
                  <span className="text-sm text-gray-600">{getI18nString('product_down')}</span>
                  {isEdit && (
                    <button
                      className="rounded-md border border-red-600 bg-white px-3 py-1 text-sm text-red-600 hover:bg-red-50"
                      onClick={handleDelete}>
                      {getI18nString('delete')}
                    </button>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {isAvailable && showStatus && (
          <div className="mt-[4px]">
            <QuantitySelector
              value={quantity}
              limitCount={limitCount}
              onQtyChange={handleQtyChange}
              inputMax={inputMax}
              disabled={isIncrementDisabled}
              style={{
                '--height': '2.8rem',
                '--button-width': '2.4rem',
                '--input-width': '3.2rem',
              }}
            />
          </div>
        )}
      </div>
    </div>
  )
}
