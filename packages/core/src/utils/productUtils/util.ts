import cloneDeep from 'lodash-es/cloneDeep'

import {
  ConfigurableProductOptions,
  ConfigurableProductOptionValue,
  ProductConfigurableOption,
  ProductDetailsData,
  ProductVariantArray,
} from '../../typings/product'

import { findMatchingVariant } from './findMatchingProductVariant'
import { isProductConfigurable } from './isProductConfigurable'
const OUT_OF_STOCK_CODE = 'OUT_OF_STOCK'
const IN_STOCK_CODE = 'IN_STOCK'

export const getMediaGalleryEntries = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
  optionCodes: Map<string, string>,
  optionSelections: Map<string, string>,
  isFirst: boolean,
) => {
  let value = []

  const { media_gallery, variants, image } = cloneDeep(product)
  const isConfigurable = isProductConfigurable(product)
  const product_media_gallery = media_gallery
    ?.filter((i) => !i?.disabled)
    .sort((a, b) => (a?.position ?? 0) - (b?.position ?? 0))

  // Selections are initialized to "code => undefined". Once we select a value, like color, the selections change. This filters out unselected options.
  const optionsSelected = Array.from(optionSelections.values()).filter((val) => !!val).length > 0

  if (!isConfigurable || !optionsSelected) {
    value = product_media_gallery ?? []
  } else {
    // If any of the possible variants matches the selection add that
    // variant's image to the media gallery. NOTE: This _can_, and does,
    // include variants such as size. If Magento is configured to display
    // an image for a size attribute, it will render that image.
    const item = findMatchingVariant({
      optionCodes,
      optionSelections,
      variants: variants as ProductVariantArray,
    })

    if (item) {
      const item_media_gallery = item.product?.media_gallery
        ?.filter((i) => !i?.disabled)
        .sort((a, b) => (a?.position ?? 0) - (b?.position ?? 0))
      if (isFirst) {
        value = [...(product_media_gallery ?? []), ...(item_media_gallery ?? [])]
      } else {
        value = [...(item_media_gallery ?? []), ...(product_media_gallery ?? [])]
      }
    } else {
      value = [...(product_media_gallery ?? [])]
    }
  }

  if (value.length === 0) {
    if (product_media_gallery?.length && product_media_gallery.length > 0) {
      value = [...product_media_gallery]
    } else {
      value = [image]
    }
  }

  return value
}

export const getProductInfo = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
  optionCodes: Map<string, string>,
  optionSelections: Map<string, string>,
) => {
  let value = {}

  const { image, sku, id, variants } = product
  const isConfigurable = isProductConfigurable(product)

  // Selections are initialized to "code => undefined". Once we select a value, like color, the selections change. This filters out unselected options.
  const optionsSelected = Array.from(optionSelections.values()).filter((val) => !!val).length > 0

  if (!isConfigurable || !optionsSelected) {
    value = {
      image,
      sku,
      id,
      productItem: product,
    }
  } else {
    // If any of the possible variants matches the selection add that
    // variant's image to the media gallery. NOTE: This _can_, and does,
    // include variants such as size. If Magento is configured to display
    // an image for a size attribute, it will render that image.
    const item = findMatchingVariant({
      optionCodes,
      optionSelections,
      variants: variants as ProductVariantArray,
    })

    value = item
      ? {
          image: item.product?.image,
          sku: item.product?.sku,
          id: item.product?.id,
          productItem: item.product,
        }
      : {
          image,
          sku,
          id,
          productItem: product,
        }
  }

  return value
}

export const getConfigPrice = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
  optionCodes: Map<string, string>,
  optionSelections: Map<string, string>,
) => {
  let value

  const { variants } = product
  const isConfigurable = isProductConfigurable(product)

  const optionsSelected = Array.from(optionSelections.values()).filter((val) => !!val).length > 0

  if (!isConfigurable || !optionsSelected) {
    value = product.price_range?.maximum_price
  } else {
    const item = findMatchingVariant({
      optionCodes,
      optionSelections,
      variants: variants as ProductVariantArray,
    })

    value = item?.product?.price_range?.maximum_price ?? product.price_range?.maximum_price
  }

  return value
}

export const getConfigAttr = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
  optionCodes: Map<string, string>,
  optionSelections: Map<string, string>,
) => {
  let value

  const { image, sku, id, options, variants, salable_qty, custom_attributesV3 } = product
  const isConfigurable = isProductConfigurable(product)

  const optionsSelected = Array.from(optionSelections.values()).filter((val) => !!val).length > 0

  if (!isConfigurable || !optionsSelected) {
    value = {
      image,
      sku,
      id,
      options,
      attributes: [],
      product: { salable_qty, custom_attributesV3 },
    }
  } else {
    const item = findMatchingVariant({
      optionCodes,
      optionSelections,
      variants: variants as ProductVariantArray,
    })

    value = item
      ? {
          ...item,
          options,
        }
      : {
          image,
          sku,
          id,
          options,
          attributes: [],
          product: { salable_qty, custom_attributesV3 },
        }
  }

  return value
}

export const getIsOutOfStock = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
  optionCodes: Map<string, string>,
  optionSelections: Map<string, string>,
) => {
  const { stock_status, variants } = product
  const isConfigurable = isProductConfigurable(product)
  const optionsSelected =
    Array.from(optionSelections.values()).filter((value) => !!value).length > 0

  if (isConfigurable && optionsSelected) {
    const item = findMatchingVariant({
      optionCodes,
      optionSelections,
      variants: variants as ProductVariantArray,
    })
    const stockStatus = item?.product?.stock_status

    return stockStatus === OUT_OF_STOCK_CODE || !stockStatus
  }
  return stock_status === OUT_OF_STOCK_CODE
}

export const getIsAllOutOfStock = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
) => {
  const { stock_status, variants } = product
  const isConfigurable = isProductConfigurable(product)

  if (isConfigurable) {
    const inStockItem = variants?.find((item) => {
      return item?.product?.stock_status === IN_STOCK_CODE
    })
    return !inStockItem
  }

  return stock_status === OUT_OF_STOCK_CODE
}

export const getProductConfigurableOptions = (
  productDetails: ProductDetailsData & { __typename: 'ConfigurableProduct' },
): ConfigurableProductOptions[] => {
  const options = productDetails.configurable_options ?? []
  const variants = productDetails.variants ?? []
  const obj: Record<string, number[]> = {}

  variants.forEach((item) => {
    item?.attributes?.forEach((attrItem) => {
      if (!attrItem?.code) return

      if (obj[attrItem.code]) {
        if (!obj[attrItem.code].includes(attrItem.value_index ?? 0)) {
          obj[attrItem.code].push(attrItem.value_index ?? 0)
        }
      } else {
        obj[attrItem.code] = [attrItem.value_index ?? 0]
      }
    })
  })

  const opt = options.map((item) => {
    const option: ConfigurableProductOptionValue[] = []
    const attributeCode = item?.attribute_code
    if (!attributeCode) return item

    obj[attributeCode]?.forEach((code) => {
      item?.values?.forEach((val) => {
        if (val?.value_index === code) {
          option.push(val)
        }
      })
    })

    return {
      ...item,
      values: option,
    }
  })

  return opt as ConfigurableProductOptions[]
}

export const isOutOfStockProductDisplayed = (
  product: ProductDetailsData & { __typename: 'ConfigurableProduct' },
) => {
  let totalVariants = 1
  const isConfigurable = isProductConfigurable(product)
  if (product.configurable_options && isConfigurable) {
    for (const option of product.configurable_options) {
      const length = option?.values?.length ?? 0
      totalVariants = totalVariants * length
    }
    return product.variants?.length === totalVariants
  }
}

export const generateCombinedProducts = (
  configurable_options: ProductConfigurableOption[],
): Array<{ value: string }> => {
  // 初始时将一个空对象放入结果数组，表示组合的起始状态
  let combinedProducts: Array<Record<string, number>> = [{}]

  // 遍历每一个属性配置项
  configurable_options.forEach((attributeConfig) => {
    const newCombinedProducts: Array<Record<string, number>> = []
    // 取出当前属性的所有可选值
    const attributeValues = attributeConfig?.values ?? []
    const attributeCode = attributeConfig?.attribute_code
    if (!attributeCode) return

    // 遍历已有的组合产品
    combinedProducts.forEach((product) => {
      // 遍历当前属性的每个可选值，生成新的组合产品
      attributeValues.forEach((value) => {
        if (!value?.value_index) return
        const newProduct = { ...product }
        newProduct[attributeCode] = value.value_index
        newCombinedProducts.push(newProduct)
      })
    })
    combinedProducts = newCombinedProducts
  })

  return combinedProducts.map((item) => ({
    value: Object.values(item).join('-'),
  }))
}
